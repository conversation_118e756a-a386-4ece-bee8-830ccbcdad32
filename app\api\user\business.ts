import { sql } from "@vercel/postgres";
import { JC_Utils_Dates, JC_Utils_Security } from "@/app/Utils";
import { UserModel } from "../../models/User";

// - GET - //

export async function GetUser(userId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "LoginFailedAttempts",
            "LoginLockoutDate",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "Id" = ${userId}
    `).rows[0];
}

export async function GetUserByStripeId(stripeCustomerId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "LoginFailedAttempts",
            "LoginLockoutDate",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "StripeCustomerId" = ${stripeCustomerId}
    `).rows[0];
}

export async function GetUserByEmail(userEmail:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "LoginFailedAttempts",
            "LoginLockoutDate",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "Email" = ${userEmail}
    `).rows[0];
}

export async function GetUserByYtMusicId(ytMusicId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "LoginFailedAttempts",
            "LoginLockoutDate",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "YtMusicId" = ${ytMusicId}
    `).rows[0];
}

export async function GetUserByToken(userToken:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "LoginFailedAttempts",
            "LoginLockoutDate",
            "ChangePasswordToken",
            "ChangePasswordTokenDate",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "ChangePasswordToken" = ${userToken}
    `).rows[0];
}


// - CREATE - //

export async function CreateUser(userData:UserModel) {
    await sql`
        INSERT INTO public."User"
        (
            "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "Phone",
            "IsAdmin",
            "IsWholesale",
            "CompanyName",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt"
        )
        VALUES
        (
            ${userData.Id},
            ${userData.YtMusicId},
            ${userData.FirstName},
            ${userData.LastName},
            ${userData.Email},
            ${userData.YtMusicAuthTokenHash},
            ${userData.YtMusicCookieHash},
            ${userData.Phone},
            ${userData.IsAdmin},
            ${userData.IsWholesale},
            ${userData.CompanyName},
            ${userData.IsEmailSubscribed},
            ${userData.IsDiscountUser},
            ${userData.StripeCustomerId},
            ${userData.IsVerified},
            ${new Date().toUTCString()}
        )
    `
}


// - UPDATE - //

export async function UpdateUser(userData:UserModel) {
    await sql`
        UPDATE public."User"
        SET "YtMusicId"         = ${userData.YtMusicId},
            "FirstName"         = ${userData.FirstName},
            "LastName"          = ${userData.LastName},
            "Email"             = ${userData.Email},
            "Phone"             = ${userData.Phone},
            "CompanyName"       = ${userData.CompanyName},
            "IsEmailSubscribed" = ${userData.IsEmailSubscribed},
            "StripeCustomerId"  = ${userData.StripeCustomerId},
            "ModifiedAt"        = ${new Date().toUTCString()},
            "Deleted"           = ${userData.Deleted}
        WHERE "Id" = ${userData.Id}
    `;
}



export async function UpdateUserStripeCustomerId(userId:string, newCustomerId:string) {
    await sql`
        UPDATE public."User"
        SET "StripeCustomerId" = ${newCustomerId}
        WHERE "Id" = ${userId}
    `;
}

export async function IncrementUserFailedAttemptsByEmail(email:string) {
    await sql`
        UPDATE public."User"
        SET "LoginFailedAttempts" = "LoginFailedAttempts"+1
        WHERE "Email" = ${email};
    `;
    let newFaildAttempts = (await sql`
        SELECT "LoginFailedAttempts"
        FROM public."User"
        WHERE "Email" = ${email};
    `).rows[0].LoginFailedAttempts;
    // IF new LoginFailedAttempts >= 5, lockout user and reset LoginFailedAttempts
    if (newFaildAttempts >= 5) {
        await sql`
            UPDATE public."User"
            SET "LoginLockoutDate" = ${new Date().toUTCString()},
                "LoginFailedAttempts" = 0
            WHERE "Email" = ${email};
        `;
        return true; // Return true if now locked out
    }
    return false;
}

export async function ResetUserFailedAttemptsByEmail(email:string) {
    await sql`
        UPDATE public."User"
        SET "LoginFailedAttempts" = 0
        WHERE "Email" = ${email};
    `;
}

export async function SetResetPasswordToken(email:string, newToken:string) {
    await sql`
        UPDATE public."User"
        SET "ChangePasswordToken" = ${newToken},
            "ChangePasswordTokenDate" = ${JC_Utils_Dates.formatDateForPostgres(new Date())}
        WHERE "Email" = ${email};
    `;
}

export async function SetUserVerificationToken(userId:string, verificationToken:string) {
    await sql`
        UPDATE public."User"
        SET "VerificationToken" = ${verificationToken}
        WHERE "Id" = ${userId}
    `;
}

export async function SetUserIsVerified(userId:string) {
    await sql`
        UPDATE public."User"
        SET "IsVerified" = true
        WHERE "Id" = ${userId}
    `;
}

export async function UpdateUserYtMusicTokens(userId:string, authTokenHash:string, cookieHash:string) {
    await sql`
        UPDATE public."User"
        SET "YtMusicAuthTokenHash" = ${authTokenHash},
            "YtMusicCookieHash" = ${cookieHash},
            "ModifiedAt" = ${new Date().toUTCString()}
        WHERE "Id" = ${userId}
    `;
}

export async function GetUserYtMusicTokens(userId:string): Promise<{authToken: string, cookie: string}> {
    const user = await GetUser(userId);

    if (!user.YtMusicAuthTokenHash || !user.YtMusicCookieHash) {
        throw new Error("User does not have YT Music tokens configured");
    }

    // Decrypt the tokens
    const authToken = JC_Utils_Security.decryptYtMusicToken(user.YtMusicAuthTokenHash);
    const cookie = JC_Utils_Security.decryptYtMusicToken(user.YtMusicCookieHash);

    return { authToken, cookie };
}