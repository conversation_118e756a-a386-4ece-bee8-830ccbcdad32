import { NextRequest, NextResponse } from "next/server";
import { UpdateUserYtMusicTokens, GetUserByYtMusicId } from "../business";
import { JC_Utils_Security } from "@/app/Utils";
import { YT_GetUserId } from "../../ytGetUserId/business";

export async function POST(request: NextRequest) {
    try {
        const { authToken, cookie } = await request.json();

        if (!authToken || !cookie) {
            return NextResponse.json({ error: "Auth token and cookie are required" }, { status: 400 });
        }

        // Encrypt the tokens before storing
        const encryptedAuthToken = JC_Utils_Security.encryptYtMusicToken(authToken);
        const encryptedCookie = JC_Utils_Security.encryptYtMusicToken(cookie);

        // Get YT Music user ID using the provided tokens
        const ytMusicUserId = await YT_GetUserId(authToken, cookie);

        if (!ytMusicUserId) {
            return NextResponse.json({ error: "Failed to get YT Music user ID" }, { status: 400 });
        }

        // Try to find user by YT Music ID
        const existingUser = await GetUserByYtMusicId(ytMusicUserId);

        if (existingUser) {
            // User found - update their tokens and log them in automatically
            await UpdateUserYtMusicTokens(existingUser.Id, encryptedAuthToken, encryptedCookie);

            // Return success with user info for automatic login
            return NextResponse.json({
                status: 200,
                message: "YT Music tokens saved successfully",
                autoLogin: true,
                user: {
                    id: existingUser.Id,
                    email: existingUser.Email,
                    ytMusicId: ytMusicUserId
                }
            });
        } else {
            // User not found - they need to register or link their account
            return NextResponse.json({
                status: 200,
                message: "YT Music user ID retrieved but no account found",
                autoLogin: false,
                ytMusicId: ytMusicUserId,
                encryptedAuthToken,
                encryptedCookie
            });
        }

    } catch (error) {
        console.error("Error saving YT Music tokens:", error);
        return NextResponse.json({ error: "Failed to save YT Music tokens" }, { status: 500 });
    }
}
