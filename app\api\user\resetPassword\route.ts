import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        // Password reset is no longer supported since we removed password authentication
        return NextResponse.json({ error: "Password reset is no longer supported. Please use YT Music authentication." }, { status: 400 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}